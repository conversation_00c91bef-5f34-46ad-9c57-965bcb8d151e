// Permission-based authorization middleware
import { Request, Response, NextFunction } from 'express';
import { PermissionService } from '@/services/permissionService';
import { AuthRequest } from './auth';

// Middleware to check if user has specific permission
export const requirePermission = (permission: string) => {
  return async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'Authentication required' });
        return;
      }

      const hasPermission = await PermissionService.userHasPermission(req.user.id, permission);
      
      if (!hasPermission) {
        res.status(403).json({ 
          error: 'Insufficient permissions',
          required: permission,
          message: `You need '${permission}' permission to access this resource`
        });
        return;
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error);
      res.status(500).json({ error: 'Permission check failed' });
    }
  };
};

// Middleware to check if user has any of the specified permissions
export const requireAnyPermission = (permissions: string[]) => {
  return async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'Authentication required' });
        return;
      }

      const hasPermission = await PermissionService.userHasAnyPermission(req.user.id, permissions);
      
      if (!hasPermission) {
        res.status(403).json({ 
          error: 'Insufficient permissions',
          required: permissions,
          message: `You need one of these permissions: ${permissions.join(', ')}`
        });
        return;
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error);
      res.status(500).json({ error: 'Permission check failed' });
    }
  };
};

// Middleware to check if user has all specified permissions
export const requireAllPermissions = (permissions: string[]) => {
  return async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'Authentication required' });
        return;
      }

      const userPermissions = await PermissionService.getUserPermissions(req.user.id);
      const hasAllPermissions = permissions.every(permission => userPermissions.includes(permission));
      
      if (!hasAllPermissions) {
        const missingPermissions = permissions.filter(permission => !userPermissions.includes(permission));
        res.status(403).json({ 
          error: 'Insufficient permissions',
          required: permissions,
          missing: missingPermissions,
          message: `You are missing these permissions: ${missingPermissions.join(', ')}`
        });
        return;
      }

      next();
    } catch (error) {
      console.error('Permission check error:', error);
      res.status(500).json({ error: 'Permission check failed' });
    }
  };
};

// Extended interface for user with permissions
interface UserWithPermissions {
  id: string;
  email: string;
  role: string;
  permissions?: string[];
}

// Middleware to add user permissions to request object
export const attachUserPermissions = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    if (req.user) {
      const permissions = await PermissionService.getUserPermissions(req.user.id);
      (req.user as UserWithPermissions).permissions = permissions;
    }
    next();
  } catch (error) {
    console.error('Attach permissions error:', error);
    next(); // Continue without permissions if there's an error
  }
};

// Helper function to check permission in route handlers
export const checkPermission = async (userId: string, permission: string): Promise<boolean> => {
  return await PermissionService.userHasPermission(userId, permission);
};

// Helper function to check multiple permissions
export const checkAnyPermission = async (userId: string, permissions: string[]): Promise<boolean> => {
  return await PermissionService.userHasAnyPermission(userId, permissions);
};

// Resource-based permission checker
export const requireResourcePermission = (
  resource: string,
  action: string,
  getResourceId?: (req: Request) => string
) => {
  return async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'Authentication required' });
        return;
      }

      const permission = `${resource}:${action}`;
      const hasPermission = await PermissionService.userHasPermission(req.user.id, permission);
      
      if (!hasPermission) {
        res.status(403).json({ 
          error: 'Insufficient permissions',
          required: permission,
          resource,
          action,
          message: `You need '${permission}' permission to ${action} ${resource}`
        });
        return;
      }

      // If resource ID is provided, you could add additional checks here
      // For example, checking if user owns the resource or has access to it
      if (getResourceId) {
        const resourceId = getResourceId(req);
        // Add additional resource-specific checks here if needed
        req.resourceId = resourceId;
      }

      next();
    } catch (error) {
      console.error('Resource permission check error:', error);
      res.status(500).json({ error: 'Permission check failed' });
    }
  };
};

// Conditional permission middleware - checks permission only if condition is met
export const requirePermissionIf = (
  condition: (req: AuthRequest) => boolean,
  permission: string
) => {
  return async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ error: 'Authentication required' });
        return;
      }

      if (condition(req)) {
        const hasPermission = await PermissionService.userHasPermission(req.user.id, permission);
        
        if (!hasPermission) {
          res.status(403).json({ 
            error: 'Insufficient permissions',
            required: permission,
            message: `You need '${permission}' permission for this operation`
          });
          return;
        }
      }

      next();
    } catch (error) {
      console.error('Conditional permission check error:', error);
      res.status(500).json({ error: 'Permission check failed' });
    }
  };
};

// Extend AuthRequest interface to include permissions and resourceId
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      resourceId?: string;
    }
  }
}


