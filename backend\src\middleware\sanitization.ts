// Input sanitization middleware
import { Request, Response, NextFunction } from 'express';

// HTML entity encoding to prevent XSS
function escapeHtml(text: string): string {
  const map: { [key: string]: string } = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#039;'
  };
  return text.replace(/[&<>"']/g, (m) => map[m]);
}

// SQL injection prevention (basic)
function sanitizeSqlInput(text: string): string {
  // Remove or escape potentially dangerous SQL characters
  return text.replace(/[';-]/g, '');
}

// Trim whitespace and normalize
function normalizeString(text: string): string {
  return text.trim().replace(/\s+/g, ' ');
}

// Sanitize object recursively
function sanitizeObject(obj: any): any {
  if (typeof obj === 'string') {
    return normalizeString(escapeHtml(obj));
  }
  
  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject);
  }
  
  if (obj && typeof obj === 'object') {
    const sanitized: any = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        sanitized[key] = sanitizeObject(obj[key]);
      }
    }
    return sanitized;
  }
  
  return obj;
}

// Middleware to sanitize request body
export const sanitizeInput = (req: Request, res: Response, next: NextFunction): void => {
  try {
    if (req.body && typeof req.body === 'object') {
      req.body = sanitizeObject(req.body);
    }
    
    if (req.query && typeof req.query === 'object') {
      req.query = sanitizeObject(req.query);
    }
    
    if (req.params && typeof req.params === 'object') {
      req.params = sanitizeObject(req.params);
    }
    
    next();
  } catch (error) {
    console.error('Sanitization error:', error);
    res.status(400).json({ error: 'Invalid input data' });
  }
};

// Email validation and sanitization
export const sanitizeEmail = (email: string): string => {
  return email.toLowerCase().trim();
};

// Phone number sanitization
export const sanitizePhone = (phone: string): string => {
  return phone.replace(/[^\d+\-\s()]/g, '').trim();
};

// Name sanitization (allow only letters, spaces, hyphens, apostrophes)
export const sanitizeName = (name: string): string => {
  return name.replace(/[^a-zA-Z\s\-']/g, '').trim();
};

// URL sanitization
export const sanitizeUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);
    // Only allow http and https protocols
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      throw new Error('Invalid protocol');
    }
    return urlObj.toString();
  } catch {
    return '';
  }
};

// Remove potentially dangerous file extensions
export const sanitizeFilename = (filename: string): string => {
  const dangerousExtensions = ['.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js'];
  let sanitized = filename.replace(/[^a-zA-Z0-9._-]/g, '');
  
  for (const ext of dangerousExtensions) {
    if (sanitized.toLowerCase().endsWith(ext)) {
      sanitized = sanitized.slice(0, -ext.length) + '.txt';
    }
  }
  
  return sanitized;
};

// Middleware for specific route sanitization
export const sanitizeAuthInput = (req: Request, res: Response, next: NextFunction): void => {
  try {
    if (req.body.email) {
      req.body.email = sanitizeEmail(req.body.email);
    }
    
    if (req.body.firstName) {
      req.body.firstName = sanitizeName(req.body.firstName);
    }
    
    if (req.body.lastName) {
      req.body.lastName = sanitizeName(req.body.lastName);
    }
    
    if (req.body.phone) {
      req.body.phone = sanitizePhone(req.body.phone);
    }
    
    if (req.body.avatar) {
      req.body.avatar = sanitizeUrl(req.body.avatar);
    }
    
    next();
  } catch (error) {
    console.error('Auth sanitization error:', error);
    res.status(400).json({ error: 'Invalid input data' });
  }
};

// Rate limiting by IP for sensitive operations
export const createRateLimitByIP = (windowMs: number, max: number) => {
  const requests = new Map<string, { count: number; resetTime: number }>();
  
  return (req: Request, res: Response, next: NextFunction): void => {
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const now = Date.now();
    
    const record = requests.get(ip);
    
    if (!record || now > record.resetTime) {
      requests.set(ip, { count: 1, resetTime: now + windowMs });
      next();
      return;
    }
    
    if (record.count >= max) {
      res.status(429).json({ 
        error: 'Too many requests from this IP. Please try again later.',
        retryAfter: Math.ceil((record.resetTime - now) / 1000)
      });
      return;
    }
    
    record.count++;
    next();
  };
};

// Clean up old rate limit records periodically
setInterval(() => {
  // This would be implemented if using the in-memory rate limiter
  // In production, use Redis or similar for distributed rate limiting
}, 60000); // Clean up every minute
