// Security management routes
import { Router, Response } from 'express';
import { SecurityService } from '@/services/securityService';
import { AuditService } from '@/services/auditService';
import { authenticateToken, AuthRequest } from '@/middleware/auth';
import { requirePermission } from '@/middleware/permissions';
import { PERMISSIONS } from '@/services/permissionService';
import { query } from '@/config/database';

const router = Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get security statistics (Admin only)
router.get('/stats', requirePermission(PERMISSIONS.SYSTEM_STATS), async (req: AuthRequest, res: Response) => {
  try {
    const timeframe = (req.query.timeframe as 'day' | 'week' | 'month') || 'week';
    const stats = await AuditService.getSecurityStats(timeframe);

    res.json({ stats });
  } catch (error) {
    console.error('Get security stats endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch security statistics' });
  }
});

// Get security events (Admin only)
router.get('/events', requirePermission(PERMISSIONS.SYSTEM_LOGS), async (req: AuthRequest, res: Response) => {
  try {
    const filters = {
      userId: req.query.userId as string,
      eventType: req.query.eventType as string,
      startDate: req.query.startDate as string,
      endDate: req.query.endDate as string,
      success: req.query.success ? req.query.success === 'true' : undefined,
      limit: parseInt(req.query.limit as string) || 50,
      offset: parseInt(req.query.offset as string) || 0,
    };

    const result = await AuditService.getSecurityEvents(filters);

    res.json(result);
  } catch (error) {
    console.error('Get security events endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch security events' });
  }
});

// Get login attempts (Admin only)
router.get('/login-attempts', requirePermission(PERMISSIONS.SYSTEM_LOGS), async (req: AuthRequest, res: Response) => {
  try {
    const filters = {
      email: req.query.email as string,
      ipAddress: req.query.ipAddress as string,
      success: req.query.success ? req.query.success === 'true' : undefined,
      startDate: req.query.startDate as string,
      endDate: req.query.endDate as string,
      limit: parseInt(req.query.limit as string) || 50,
      offset: parseInt(req.query.offset as string) || 0,
    };

    const result = await AuditService.getLoginAttempts(filters);

    res.json(result);
  } catch (error) {
    console.error('Get login attempts endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch login attempts' });
  }
});

// Validate password strength
router.post('/validate-password', async (req: AuthRequest, res: Response) => {
  try {
    const { password, userInfo } = req.body;

    if (!password) {
      return res.status(400).json({ error: 'Password is required' });
    }

    const validation = SecurityService.validatePassword(password, userInfo);

    res.json({
      isValid: validation.isValid,
      errors: validation.errors,
      strength: calculatePasswordStrength(password)
    });
  } catch (error) {
    console.error('Validate password endpoint error:', error);
    res.status(500).json({ error: 'Failed to validate password' });
  }
});

// Check password history
router.post('/check-password-history', async (req: AuthRequest, res: Response) => {
  try {
    const { password } = req.body;
    const userId = req.user!.id;

    if (!password) {
      return res.status(400).json({ error: 'Password is required' });
    }

    const isNewPassword = await SecurityService.checkPasswordHistory(userId, password);

    res.json({
      isNewPassword,
      message: isNewPassword ? 'Password is acceptable' : 'Password was used recently'
    });
  } catch (error) {
    console.error('Check password history endpoint error:', error);
    res.status(500).json({ error: 'Failed to check password history' });
  }
});

// Get user's active sessions
router.get('/sessions', async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user!.id;

    const result = await query(
      `SELECT id, ip_address, user_agent, device_info, last_activity, created_at, expires_at
       FROM user_sessions 
       WHERE user_id = $1 AND expires_at > NOW()
       ORDER BY last_activity DESC`,
      [userId]
    );

    res.json({ sessions: result.rows });
  } catch (error) {
    console.error('Get user sessions endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch user sessions' });
  }
});

// Revoke a specific session
router.delete('/sessions/:sessionId', async (req: AuthRequest, res: Response) => {
  try {
    const { sessionId } = req.params;
    const userId = req.user!.id;

    // Verify session belongs to user
    const sessionCheck = await query(
      'SELECT user_id FROM user_sessions WHERE id = $1',
      [sessionId]
    );

    if (sessionCheck.rows.length === 0) {
      return res.status(404).json({ error: 'Session not found' });
    }

    if (sessionCheck.rows[0].user_id !== userId) {
      return res.status(403).json({ error: 'Cannot revoke other users sessions' });
    }

    await SecurityService.revokeSession(sessionId);

    await AuditService.logSecurityEvent({
      userId,
      eventType: 'session_revoked',
      eventDescription: 'User revoked a session',
      ...AuditService.extractRequestInfo(req),
      metadata: { sessionId }
    });

    res.json({ message: 'Session revoked successfully' });
  } catch (error) {
    console.error('Revoke session endpoint error:', error);
    res.status(500).json({ error: 'Failed to revoke session' });
  }
});

// Revoke all sessions except current
router.post('/sessions/revoke-all', async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const currentSessionId = req.headers['x-session-id'] as string;

    // Get all user sessions except current
    const sessions = await query(
      'SELECT id FROM user_sessions WHERE user_id = $1 AND id != $2',
      [userId, currentSessionId || '']
    );

    // Revoke all other sessions
    for (const session of sessions.rows) {
      await SecurityService.revokeSession(session.id);
    }

    await AuditService.logSecurityEvent({
      userId,
      eventType: 'all_sessions_revoked',
      eventDescription: 'User revoked all other sessions',
      ...AuditService.extractRequestInfo(req),
      metadata: { revokedCount: sessions.rows.length }
    });

    res.json({ 
      message: 'All other sessions revoked successfully',
      revokedCount: sessions.rows.length
    });
  } catch (error) {
    console.error('Revoke all sessions endpoint error:', error);
    res.status(500).json({ error: 'Failed to revoke sessions' });
  }
});

// Get user's security events
router.get('/my-events', async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const limit = parseInt(req.query.limit as string) || 20;
    const offset = parseInt(req.query.offset as string) || 0;

    const result = await AuditService.getSecurityEvents({
      userId,
      limit,
      offset
    });

    res.json(result);
  } catch (error) {
    console.error('Get user security events endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch security events' });
  }
});

// Check if password is expired
router.get('/password-status', async (req: AuthRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const isExpired = await SecurityService.isPasswordExpired(userId);

    res.json({
      isExpired,
      message: isExpired ? 'Password has expired and needs to be changed' : 'Password is current'
    });
  } catch (error) {
    console.error('Check password status endpoint error:', error);
    res.status(500).json({ error: 'Failed to check password status' });
  }
});

// Helper function to calculate password strength
function calculatePasswordStrength(password: string): {
  score: number;
  level: 'weak' | 'fair' | 'good' | 'strong';
  feedback: string[];
} {
  let score = 0;
  const feedback: string[] = [];

  // Length scoring
  if (password.length >= 8) score += 1;
  if (password.length >= 12) score += 1;
  if (password.length >= 16) score += 1;

  // Character variety scoring
  if (/[a-z]/.test(password)) score += 1;
  if (/[A-Z]/.test(password)) score += 1;
  if (/\d/.test(password)) score += 1;
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1;

  // Complexity patterns
  if (/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) score += 1;
  if (/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])/.test(password)) score += 1;

  // Feedback generation
  if (password.length < 8) feedback.push('Use at least 8 characters');
  if (!/[a-z]/.test(password)) feedback.push('Add lowercase letters');
  if (!/[A-Z]/.test(password)) feedback.push('Add uppercase letters');
  if (!/\d/.test(password)) feedback.push('Add numbers');
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) feedback.push('Add special characters');

  // Determine level
  let level: 'weak' | 'fair' | 'good' | 'strong';
  if (score <= 3) level = 'weak';
  else if (score <= 5) level = 'fair';
  else if (score <= 7) level = 'good';
  else level = 'strong';

  return { score, level, feedback };
}

export default router;
