// Audit logging service for security events and user activities
import { query } from '@/config/database';
import { Request } from 'express';

export interface SecurityEvent {
  userId?: string;
  eventType: string;
  eventDescription: string;
  ipAddress?: string;
  userAgent?: string;
  success?: boolean;
  metadata?: any;
}

export interface LoginAttempt {
  email: string;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  failureReason?: string;
}

export class AuditService {
  // Log security events
  static async logSecurityEvent(event: SecurityEvent): Promise<string> {
    try {
      const result = await query(
        `INSERT INTO security_events 
         (user_id, event_type, event_description, ip_address, user_agent, success, metadata, created_at)
         VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
         RETURNING id`,
        [
          event.userId || null,
          event.eventType,
          event.eventDescription,
          event.ipAddress || null,
          event.userAgent || null,
          event.success !== false, // Default to true
          event.metadata ? JSON.stringify(event.metadata) : null
        ]
      );

      return result.rows[0].id;
    } catch (error) {
      console.error('Log security event error:', error);
      throw new Error('Failed to log security event');
    }
  }

  // Log login attempts
  static async logLoginAttempt(attempt: LoginAttempt): Promise<void> {
    try {
      await query(
        `INSERT INTO login_attempts 
         (email, ip_address, user_agent, success, failure_reason, created_at)
         VALUES ($1, $2, $3, $4, $5, NOW())`,
        [
          attempt.email,
          attempt.ipAddress || null,
          attempt.userAgent || null,
          attempt.success,
          attempt.failureReason || null
        ]
      );
    } catch (error) {
      console.error('Log login attempt error:', error);
      // Don't throw error for logging failures
    }
  }

  // Get security events with filtering
  static async getSecurityEvents(filters: {
    userId?: string;
    eventType?: string;
    startDate?: string;
    endDate?: string;
    success?: boolean;
    limit?: number;
    offset?: number;
  } = {}): Promise<{ events: any[]; total: number }> {
    try {
      const {
        userId,
        eventType,
        startDate,
        endDate,
        success,
        limit = 50,
        offset = 0
      } = filters;

      const conditions: string[] = [];
      const params: any[] = [];
      let paramIndex = 1;

      if (userId) {
        conditions.push(`user_id = $${paramIndex++}`);
        params.push(userId);
      }

      if (eventType) {
        conditions.push(`event_type = $${paramIndex++}`);
        params.push(eventType);
      }

      if (startDate) {
        conditions.push(`created_at >= $${paramIndex++}`);
        params.push(startDate);
      }

      if (endDate) {
        conditions.push(`created_at <= $${paramIndex++}`);
        params.push(endDate);
      }

      if (success !== undefined) {
        conditions.push(`success = $${paramIndex++}`);
        params.push(success);
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      // Get total count
      const countResult = await query(
        `SELECT COUNT(*) as total FROM security_events ${whereClause}`,
        params
      );

      const total = parseInt(countResult.rows[0].total);

      // Get events with pagination
      params.push(limit, offset);
      const result = await query(
        `SELECT se.*, u.email as user_email, p.first_name, p.last_name
         FROM security_events se
         LEFT JOIN users u ON se.user_id = u.id
         LEFT JOIN profiles p ON u.id = p.id
         ${whereClause}
         ORDER BY se.created_at DESC
         LIMIT $${paramIndex++} OFFSET $${paramIndex++}`,
        params
      );

      return {
        events: result.rows,
        total
      };
    } catch (error) {
      console.error('Get security events error:', error);
      throw new Error('Failed to fetch security events');
    }
  }

  // Get login attempts with filtering
  static async getLoginAttempts(filters: {
    email?: string;
    ipAddress?: string;
    success?: boolean;
    startDate?: string;
    endDate?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<{ attempts: any[]; total: number }> {
    try {
      const {
        email,
        ipAddress,
        success,
        startDate,
        endDate,
        limit = 50,
        offset = 0
      } = filters;

      const conditions: string[] = [];
      const params: any[] = [];
      let paramIndex = 1;

      if (email) {
        conditions.push(`email ILIKE $${paramIndex++}`);
        params.push(`%${email}%`);
      }

      if (ipAddress) {
        conditions.push(`ip_address = $${paramIndex++}`);
        params.push(ipAddress);
      }

      if (success !== undefined) {
        conditions.push(`success = $${paramIndex++}`);
        params.push(success);
      }

      if (startDate) {
        conditions.push(`created_at >= $${paramIndex++}`);
        params.push(startDate);
      }

      if (endDate) {
        conditions.push(`created_at <= $${paramIndex++}`);
        params.push(endDate);
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      // Get total count
      const countResult = await query(
        `SELECT COUNT(*) as total FROM login_attempts ${whereClause}`,
        params
      );

      const total = parseInt(countResult.rows[0].total);

      // Get attempts with pagination
      params.push(limit, offset);
      const result = await query(
        `SELECT * FROM login_attempts
         ${whereClause}
         ORDER BY created_at DESC
         LIMIT $${paramIndex++} OFFSET $${paramIndex++}`,
        params
      );

      return {
        attempts: result.rows,
        total
      };
    } catch (error) {
      console.error('Get login attempts error:', error);
      throw new Error('Failed to fetch login attempts');
    }
  }

  // Get security statistics
  static async getSecurityStats(timeframe: 'day' | 'week' | 'month' = 'week'): Promise<any> {
    try {
      let interval: string;
      switch (timeframe) {
        case 'day':
          interval = '24 hours';
          break;
        case 'week':
          interval = '7 days';
          break;
        case 'month':
          interval = '30 days';
          break;
        default:
          interval = '7 days';
      }

      const stats = await Promise.all([
        // Security events summary
        query(`
          SELECT 
            event_type,
            COUNT(*) as count,
            COUNT(CASE WHEN success = false THEN 1 END) as failures
          FROM security_events 
          WHERE created_at > NOW() - INTERVAL '${interval}'
          GROUP BY event_type
          ORDER BY count DESC
        `),

        // Login attempts summary
        query(`
          SELECT 
            COUNT(*) as total_attempts,
            COUNT(CASE WHEN success = true THEN 1 END) as successful_logins,
            COUNT(CASE WHEN success = false THEN 1 END) as failed_logins,
            COUNT(DISTINCT email) as unique_users,
            COUNT(DISTINCT ip_address) as unique_ips
          FROM login_attempts 
          WHERE created_at > NOW() - INTERVAL '${interval}'
        `),

        // Top failed login IPs
        query(`
          SELECT 
            ip_address,
            COUNT(*) as failed_attempts
          FROM login_attempts 
          WHERE created_at > NOW() - INTERVAL '${interval}' AND success = false
          GROUP BY ip_address
          ORDER BY failed_attempts DESC
          LIMIT 10
        `),

        // Account lockouts
        query(`
          SELECT COUNT(*) as locked_accounts
          FROM users 
          WHERE locked_until > NOW()
        `),

        // Active sessions
        query(`
          SELECT COUNT(*) as active_sessions
          FROM user_sessions 
          WHERE expires_at > NOW()
        `)
      ]);

      return {
        timeframe,
        eventsSummary: stats[0].rows,
        loginSummary: stats[1].rows[0],
        topFailedIPs: stats[2].rows,
        lockedAccounts: parseInt(stats[3].rows[0].locked_accounts),
        activeSessions: parseInt(stats[4].rows[0].active_sessions)
      };
    } catch (error) {
      console.error('Get security stats error:', error);
      throw new Error('Failed to fetch security statistics');
    }
  }

  // Helper method to extract request info
  static extractRequestInfo(req: Request): {
    ipAddress?: string;
    userAgent?: string;
  } {
    return {
      ipAddress: req.ip || req.connection.remoteAddress || undefined,
      userAgent: req.get('User-Agent') || undefined
    };
  }

  // Convenience methods for common events
  static async logUserLogin(userId: string, req: Request, success: boolean = true): Promise<void> {
    const requestInfo = this.extractRequestInfo(req);
    
    await this.logSecurityEvent({
      userId,
      eventType: success ? 'login_success' : 'login_failure',
      eventDescription: success ? 'User logged in successfully' : 'User login failed',
      ipAddress: requestInfo.ipAddress,
      userAgent: requestInfo.userAgent,
      success
    });
  }

  static async logUserLogout(userId: string, req: Request): Promise<void> {
    const requestInfo = this.extractRequestInfo(req);
    
    await this.logSecurityEvent({
      userId,
      eventType: 'logout',
      eventDescription: 'User logged out',
      ipAddress: requestInfo.ipAddress,
      userAgent: requestInfo.userAgent,
      success: true
    });
  }

  static async logPasswordChange(userId: string, req: Request): Promise<void> {
    const requestInfo = this.extractRequestInfo(req);
    
    await this.logSecurityEvent({
      userId,
      eventType: 'password_change',
      eventDescription: 'User changed password',
      ipAddress: requestInfo.ipAddress,
      userAgent: requestInfo.userAgent,
      success: true
    });
  }

  static async logAccountLockout(userId: string, req: Request, failedAttempts: number): Promise<void> {
    const requestInfo = this.extractRequestInfo(req);
    
    await this.logSecurityEvent({
      userId,
      eventType: 'account_lockout',
      eventDescription: 'Account locked due to failed login attempts',
      ipAddress: requestInfo.ipAddress,
      userAgent: requestInfo.userAgent,
      success: false,
      metadata: { failedAttempts }
    });
  }

  static async logPermissionDenied(userId: string, req: Request, resource: string, action: string): Promise<void> {
    const requestInfo = this.extractRequestInfo(req);
    
    await this.logSecurityEvent({
      userId,
      eventType: 'permission_denied',
      eventDescription: `Access denied to ${resource}:${action}`,
      ipAddress: requestInfo.ipAddress,
      userAgent: requestInfo.userAgent,
      success: false,
      metadata: { resource, action }
    });
  }
}
