// Security service for password policies, account lockout, and audit logging
import { query } from '@/config/database';
import bcrypt from 'bcryptjs';

// Password policy configuration
export const PASSWORD_POLICY = {
  minLength: 8,
  maxLength: 128,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  preventCommonPasswords: true,
  preventUserInfoInPassword: true,
  passwordHistoryCount: 5, // Remember last 5 passwords
  maxAge: 90, // Password expires after 90 days
};

// Account lockout configuration
export const LOCKOUT_POLICY = {
  maxFailedAttempts: 5,
  lockoutDuration: 30, // minutes
  resetFailedAttemptsAfter: 60, // minutes
};

// Session configuration
export const SESSION_POLICY = {
  maxAge: 24 * 60 * 60 * 1000, // 24 hours
  maxConcurrentSessions: 3,
  requireReauthForSensitive: true,
  sessionTimeoutWarning: 5 * 60 * 1000, // 5 minutes before expiry
};

export class SecurityService {
  // Password validation
  static validatePassword(password: string, userInfo?: {
    email?: string;
    firstName?: string;
    lastName?: string;
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Length check
    if (password.length < PASSWORD_POLICY.minLength) {
      errors.push(`Password must be at least ${PASSWORD_POLICY.minLength} characters long`);
    }
    if (password.length > PASSWORD_POLICY.maxLength) {
      errors.push(`Password must be no more than ${PASSWORD_POLICY.maxLength} characters long`);
    }

    // Character requirements
    if (PASSWORD_POLICY.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    if (PASSWORD_POLICY.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    if (PASSWORD_POLICY.requireNumbers && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    if (PASSWORD_POLICY.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    // Common password check
    if (PASSWORD_POLICY.preventCommonPasswords) {
      const commonPasswords = [
        'password', '123456', '123456789', 'qwerty', 'abc123',
        'password123', 'admin', 'letmein', 'welcome', 'monkey'
      ];
      if (commonPasswords.includes(password.toLowerCase())) {
        errors.push('Password is too common, please choose a more secure password');
      }
    }

    // User info in password check
    if (PASSWORD_POLICY.preventUserInfoInPassword && userInfo) {
      const userInfoValues = [
        userInfo.email?.split('@')[0],
        userInfo.firstName,
        userInfo.lastName
      ].filter(Boolean);

      for (const info of userInfoValues) {
        if (info && password.toLowerCase().includes(info.toLowerCase())) {
          errors.push('Password should not contain your personal information');
          break;
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Check password history
  static async checkPasswordHistory(userId: string, newPassword: string): Promise<boolean> {
    try {
      const result = await query(
        `SELECT password_hash FROM password_history 
         WHERE user_id = $1 
         ORDER BY created_at DESC 
         LIMIT $2`,
        [userId, PASSWORD_POLICY.passwordHistoryCount]
      );

      for (const row of result.rows) {
        const isMatch = await bcrypt.compare(newPassword, row.password_hash);
        if (isMatch) {
          return false; // Password was used before
        }
      }

      return true; // Password is new
    } catch (error) {
      console.error('Check password history error:', error);
      return true; // Allow password change if check fails
    }
  }

  // Save password to history
  static async savePasswordHistory(userId: string, passwordHash: string): Promise<void> {
    try {
      // Add new password to history
      await query(
        'INSERT INTO password_history (user_id, password_hash, created_at) VALUES ($1, $2, NOW())',
        [userId, passwordHash]
      );

      // Clean up old password history
      await query(
        `DELETE FROM password_history 
         WHERE user_id = $1 
         AND id NOT IN (
           SELECT id FROM password_history 
           WHERE user_id = $1 
           ORDER BY created_at DESC 
           LIMIT $2
         )`,
        [userId, PASSWORD_POLICY.passwordHistoryCount]
      );
    } catch (error) {
      console.error('Save password history error:', error);
      // Don't throw error, just log it
    }
  }

  // Check if password is expired
  static async isPasswordExpired(userId: string): Promise<boolean> {
    try {
      const result = await query(
        'SELECT password_changed_at FROM users WHERE id = $1',
        [userId]
      );

      if (result.rows.length === 0) {
        return false;
      }

      const passwordChangedAt = result.rows[0].password_changed_at;
      if (!passwordChangedAt) {
        return false; // No password change date, assume not expired
      }

      const daysSinceChange = (Date.now() - new Date(passwordChangedAt).getTime()) / (1000 * 60 * 60 * 24);
      return daysSinceChange > PASSWORD_POLICY.maxAge;
    } catch (error) {
      console.error('Check password expiry error:', error);
      return false;
    }
  }

  // Account lockout management
  static async checkAccountLockout(userId: string): Promise<{
    isLocked: boolean;
    lockedUntil?: Date;
    failedAttempts: number;
  }> {
    try {
      const result = await query(
        'SELECT failed_login_attempts, locked_until, last_failed_login FROM users WHERE id = $1',
        [userId]
      );

      if (result.rows.length === 0) {
        return { isLocked: false, failedAttempts: 0 };
      }

      const user = result.rows[0];
      const now = new Date();
      const lockedUntil = user.locked_until ? new Date(user.locked_until) : null;

      // Check if account is currently locked
      if (lockedUntil && lockedUntil > now) {
        return {
          isLocked: true,
          lockedUntil,
          failedAttempts: user.failed_login_attempts || 0
        };
      }

      // Check if failed attempts should be reset
      const lastFailedLogin = user.last_failed_login ? new Date(user.last_failed_login) : null;
      if (lastFailedLogin) {
        const minutesSinceLastFailed = (now.getTime() - lastFailedLogin.getTime()) / (1000 * 60);
        if (minutesSinceLastFailed > LOCKOUT_POLICY.resetFailedAttemptsAfter) {
          // Reset failed attempts
          await query(
            'UPDATE users SET failed_login_attempts = 0, last_failed_login = NULL WHERE id = $1',
            [userId]
          );
          return { isLocked: false, failedAttempts: 0 };
        }
      }

      return {
        isLocked: false,
        failedAttempts: user.failed_login_attempts || 0
      };
    } catch (error) {
      console.error('Check account lockout error:', error);
      return { isLocked: false, failedAttempts: 0 };
    }
  }

  // Record failed login attempt
  static async recordFailedLogin(userId: string): Promise<{
    shouldLock: boolean;
    failedAttempts: number;
    lockedUntil?: Date;
  }> {
    try {
      const result = await query(
        'SELECT failed_login_attempts FROM users WHERE id = $1',
        [userId]
      );

      if (result.rows.length === 0) {
        return { shouldLock: false, failedAttempts: 0 };
      }

      const currentAttempts = (result.rows[0].failed_login_attempts || 0) + 1;
      const shouldLock = currentAttempts >= LOCKOUT_POLICY.maxFailedAttempts;

      if (shouldLock) {
        const lockedUntil = new Date(Date.now() + LOCKOUT_POLICY.lockoutDuration * 60 * 1000);
        
        await query(
          `UPDATE users SET 
           failed_login_attempts = $1, 
           locked_until = $2, 
           last_failed_login = NOW(),
           updated_at = NOW()
           WHERE id = $3`,
          [currentAttempts, lockedUntil, userId]
        );

        return { shouldLock: true, failedAttempts: currentAttempts, lockedUntil };
      } else {
        await query(
          `UPDATE users SET 
           failed_login_attempts = $1, 
           last_failed_login = NOW(),
           updated_at = NOW()
           WHERE id = $2`,
          [currentAttempts, userId]
        );

        return { shouldLock: false, failedAttempts: currentAttempts };
      }
    } catch (error) {
      console.error('Record failed login error:', error);
      return { shouldLock: false, failedAttempts: 0 };
    }
  }

  // Reset failed login attempts (on successful login)
  static async resetFailedAttempts(userId: string): Promise<void> {
    try {
      await query(
        `UPDATE users SET 
         failed_login_attempts = 0, 
         locked_until = NULL, 
         last_failed_login = NULL,
         last_login = NOW(),
         updated_at = NOW()
         WHERE id = $1`,
        [userId]
      );
    } catch (error) {
      console.error('Reset failed attempts error:', error);
    }
  }

  // Session management
  static async createSession(userId: string, sessionData: {
    ipAddress?: string;
    userAgent?: string;
    deviceInfo?: string;
  }): Promise<string> {
    try {
      // Check concurrent sessions limit
      const activeSessions = await query(
        'SELECT COUNT(*) as count FROM user_sessions WHERE user_id = $1 AND expires_at > NOW()',
        [userId]
      );

      const sessionCount = parseInt(activeSessions.rows[0].count);
      if (sessionCount >= SESSION_POLICY.maxConcurrentSessions) {
        // Remove oldest session
        await query(
          `DELETE FROM user_sessions 
           WHERE user_id = $1 
           AND id = (
             SELECT id FROM user_sessions 
             WHERE user_id = $1 AND expires_at > NOW()
             ORDER BY created_at ASC 
             LIMIT 1
           )`,
          [userId]
        );
      }

      // Create new session
      const sessionId = require('crypto').randomBytes(32).toString('hex');
      const expiresAt = new Date(Date.now() + SESSION_POLICY.maxAge);

      await query(
        `INSERT INTO user_sessions 
         (id, user_id, ip_address, user_agent, device_info, expires_at, created_at) 
         VALUES ($1, $2, $3, $4, $5, $6, NOW())`,
        [sessionId, userId, sessionData.ipAddress, sessionData.userAgent, sessionData.deviceInfo, expiresAt]
      );

      return sessionId;
    } catch (error) {
      console.error('Create session error:', error);
      throw new Error('Failed to create session');
    }
  }

  // Validate session
  static async validateSession(sessionId: string): Promise<{
    isValid: boolean;
    userId?: string;
    needsRefresh?: boolean;
  }> {
    try {
      const result = await query(
        `SELECT user_id, expires_at, last_activity 
         FROM user_sessions 
         WHERE id = $1 AND expires_at > NOW()`,
        [sessionId]
      );

      if (result.rows.length === 0) {
        return { isValid: false };
      }

      const session = result.rows[0];
      const now = new Date();
      const expiresAt = new Date(session.expires_at);
      const timeUntilExpiry = expiresAt.getTime() - now.getTime();

      // Update last activity
      await query(
        'UPDATE user_sessions SET last_activity = NOW() WHERE id = $1',
        [sessionId]
      );

      return {
        isValid: true,
        userId: session.user_id,
        needsRefresh: timeUntilExpiry < SESSION_POLICY.sessionTimeoutWarning
      };
    } catch (error) {
      console.error('Validate session error:', error);
      return { isValid: false };
    }
  }

  // Revoke session
  static async revokeSession(sessionId: string): Promise<void> {
    try {
      await query('DELETE FROM user_sessions WHERE id = $1', [sessionId]);
    } catch (error) {
      console.error('Revoke session error:', error);
    }
  }

  // Revoke all user sessions
  static async revokeAllUserSessions(userId: string): Promise<void> {
    try {
      await query('DELETE FROM user_sessions WHERE user_id = $1', [userId]);
    } catch (error) {
      console.error('Revoke all user sessions error:', error);
    }
  }
}
