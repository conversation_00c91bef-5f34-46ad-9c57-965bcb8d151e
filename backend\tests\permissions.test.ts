// Permission service tests
import { PermissionService, PERMISSIONS } from '../src/services/permissionService';
import { query } from '../src/config/database';

// Mock the database query function
jest.mock('../src/config/database', () => ({
  query: jest.fn(),
}));

const mockQuery = query as jest.MockedFunction<typeof query>;

describe('PermissionService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('userHasPermission', () => {
    it('should return true when user has the permission', async () => {
      mockQuery.mockResolvedValueOnce({
        rows: [{ permissions: ['user:read', 'user:create'] }]
      });

      const result = await PermissionService.userHasPermission('user-id', PERMISSIONS.USER_READ);

      expect(result).toBe(true);
      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('SELECT r.permissions'),
        ['user-id']
      );
    });

    it('should return false when user does not have the permission', async () => {
      mockQuery.mockResolvedValueOnce({
        rows: [{ permissions: ['user:read'] }]
      });

      const result = await PermissionService.userHasPermission('user-id', PERMISSIONS.USER_DELETE);

      expect(result).toBe(false);
    });

    it('should return false when user has no role', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [] });

      const result = await PermissionService.userHasPermission('user-id', PERMISSIONS.USER_READ);

      expect(result).toBe(false);
    });

    it('should return true for system admin with any permission', async () => {
      mockQuery.mockResolvedValueOnce({
        rows: [{ permissions: [PERMISSIONS.SYSTEM_ADMIN] }]
      });

      const result = await PermissionService.userHasPermission('user-id', PERMISSIONS.USER_DELETE);

      expect(result).toBe(true);
    });
  });

  describe('userHasAnyPermission', () => {
    it('should return true when user has at least one permission', async () => {
      mockQuery.mockResolvedValueOnce({
        rows: [{ permissions: ['user:read', 'employee:read'] }]
      });

      const result = await PermissionService.userHasAnyPermission('user-id', [
        PERMISSIONS.USER_CREATE,
        PERMISSIONS.USER_READ,
        PERMISSIONS.USER_DELETE
      ]);

      expect(result).toBe(true);
    });

    it('should return false when user has none of the permissions', async () => {
      mockQuery.mockResolvedValueOnce({
        rows: [{ permissions: ['employee:read'] }]
      });

      const result = await PermissionService.userHasAnyPermission('user-id', [
        PERMISSIONS.USER_CREATE,
        PERMISSIONS.USER_UPDATE,
        PERMISSIONS.USER_DELETE
      ]);

      expect(result).toBe(false);
    });
  });

  describe('getUserPermissions', () => {
    it('should return user permissions', async () => {
      const expectedPermissions = ['user:read', 'user:create', 'employee:read'];
      mockQuery.mockResolvedValueOnce({
        rows: [{ permissions: expectedPermissions }]
      });

      const result = await PermissionService.getUserPermissions('user-id');

      expect(result).toEqual(expectedPermissions);
    });

    it('should return empty array when user has no permissions', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [] });

      const result = await PermissionService.getUserPermissions('user-id');

      expect(result).toEqual([]);
    });
  });

  describe('updateRolePermissions', () => {
    it('should update role permissions successfully', async () => {
      const permissions = ['user:read', 'user:create'];
      mockQuery.mockResolvedValueOnce({ rows: [] });

      await PermissionService.updateRolePermissions('role-id', permissions);

      expect(mockQuery).toHaveBeenCalledWith(
        'UPDATE roles SET permissions = $1, updated_at = NOW() WHERE id = $2',
        [JSON.stringify(permissions), 'role-id']
      );
    });
  });

  describe('validatePermissions', () => {
    it('should validate permissions correctly', () => {
      const permissions = [
        PERMISSIONS.USER_READ,
        PERMISSIONS.USER_CREATE,
        'invalid:permission',
        PERMISSIONS.EMPLOYEE_READ,
        'another:invalid'
      ];

      const result = PermissionService.validatePermissions(permissions);

      expect(result.valid).toContain(PERMISSIONS.USER_READ);
      expect(result.valid).toContain(PERMISSIONS.USER_CREATE);
      expect(result.valid).toContain(PERMISSIONS.EMPLOYEE_READ);
      expect(result.invalid).toContain('invalid:permission');
      expect(result.invalid).toContain('another:invalid');
    });

    it('should return all valid when all permissions are valid', () => {
      const permissions = [
        PERMISSIONS.USER_READ,
        PERMISSIONS.USER_CREATE,
        PERMISSIONS.EMPLOYEE_READ
      ];

      const result = PermissionService.validatePermissions(permissions);

      expect(result.valid).toEqual(permissions);
      expect(result.invalid).toEqual([]);
    });
  });

  describe('getDefaultPermissions', () => {
    it('should return admin permissions for admin role', () => {
      const permissions = PermissionService.getDefaultPermissions('admin');

      expect(permissions).toContain(PERMISSIONS.SYSTEM_ADMIN);
      expect(permissions).toContain(PERMISSIONS.USER_CREATE);
      expect(permissions).toContain(PERMISSIONS.USER_UPDATE);
      expect(permissions).toContain(PERMISSIONS.USER_DELETE);
    });

    it('should return manager permissions for manager role', () => {
      const permissions = PermissionService.getDefaultPermissions('manager');

      expect(permissions).toContain(PERMISSIONS.EMPLOYEE_READ);
      expect(permissions).toContain(PERMISSIONS.EMPLOYEE_CREATE);
      expect(permissions).toContain(PERMISSIONS.SCHEDULE_READ);
      expect(permissions).toContain(PERMISSIONS.LEAVE_APPROVE);
    });

    it('should return basic permissions for nurse role', () => {
      const permissions = PermissionService.getDefaultPermissions('nurse');

      expect(permissions).toContain(PERMISSIONS.SCHEDULE_READ);
      expect(permissions).toContain(PERMISSIONS.LEAVE_CREATE);
      expect(permissions.length).toBeGreaterThan(0);
      expect(permissions).not.toContain(PERMISSIONS.USER_CREATE);
    });

    it('should return empty array for unknown role', () => {
      const permissions = PermissionService.getDefaultPermissions('unknown');

      expect(permissions).toEqual([]);
    });
  });

  describe('getAllPermissions', () => {
    it('should return all available permissions', () => {
      const permissions = PermissionService.getAllPermissions();

      expect(permissions).toContain(PERMISSIONS.USER_READ);
      expect(permissions).toContain(PERMISSIONS.USER_CREATE);
      expect(permissions).toContain(PERMISSIONS.EMPLOYEE_READ);
      expect(permissions).toContain(PERMISSIONS.SCHEDULE_READ);
      expect(permissions.length).toBeGreaterThan(10);
    });
  });

  describe('getPermissionGroups', () => {
    it('should return permission groups', () => {
      const groups = PermissionService.getPermissionGroups();

      expect(groups).toHaveProperty('user');
      expect(groups).toHaveProperty('employee');
      expect(groups).toHaveProperty('schedule');
      expect(groups).toHaveProperty('leave');

      expect(groups.user.name).toBe('User Management');
      expect(groups.user.permissions).toContain(PERMISSIONS.USER_READ);
      expect(groups.user.permissions).toContain(PERMISSIONS.USER_CREATE);

      expect(groups.employee.name).toBe('Employee Management');
      expect(groups.employee.permissions).toContain(PERMISSIONS.EMPLOYEE_READ);
      expect(groups.employee.permissions).toContain(PERMISSIONS.EMPLOYEE_CREATE);
    });
  });

  describe('permission constants', () => {
    it('should have all required permission constants', () => {
      expect(PERMISSIONS.SYSTEM_ADMIN).toBe('system:admin');
      expect(PERMISSIONS.USER_READ).toBe('user:read');
      expect(PERMISSIONS.USER_CREATE).toBe('user:create');
      expect(PERMISSIONS.USER_UPDATE).toBe('user:update');
      expect(PERMISSIONS.USER_DELETE).toBe('user:delete');
      expect(PERMISSIONS.EMPLOYEE_READ).toBe('employee:read');
      expect(PERMISSIONS.EMPLOYEE_CREATE).toBe('employee:create');
      expect(PERMISSIONS.SCHEDULE_READ).toBe('schedule:read');
      expect(PERMISSIONS.LEAVE_CREATE).toBe('leave:create');
      expect(PERMISSIONS.LEAVE_APPROVE).toBe('leave:approve');
    });
  });
});

// Integration tests for permission middleware
describe('Permission Middleware Integration', () => {
  const mockRequest = {
    user: { id: 'user-id', role: 'nurse' }
  } as any;

  const mockResponse = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn()
  } as any;

  const mockNext = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should allow access when user has required permission', async () => {
    mockQuery.mockResolvedValueOnce({
      rows: [{ permissions: [PERMISSIONS.SCHEDULE_READ] }]
    });

    const { requirePermission } = require('../src/middleware/permissions');
    const middleware = requirePermission(PERMISSIONS.SCHEDULE_READ);

    await middleware(mockRequest, mockResponse, mockNext);

    expect(mockNext).toHaveBeenCalled();
    expect(mockResponse.status).not.toHaveBeenCalled();
  });

  it('should deny access when user lacks required permission', async () => {
    mockQuery.mockResolvedValueOnce({
      rows: [{ permissions: [PERMISSIONS.SCHEDULE_READ] }]
    });

    const { requirePermission } = require('../src/middleware/permissions');
    const middleware = requirePermission(PERMISSIONS.USER_DELETE);

    await middleware(mockRequest, mockResponse, mockNext);

    expect(mockNext).not.toHaveBeenCalled();
    expect(mockResponse.status).toHaveBeenCalledWith(403);
    expect(mockResponse.json).toHaveBeenCalledWith({
      error: 'Insufficient permissions',
      required: PERMISSIONS.USER_DELETE,
      message: `You need '${PERMISSIONS.USER_DELETE}' permission to access this resource`
    });
  });

  it('should deny access when user is not authenticated', async () => {
    const unauthenticatedRequest = {} as any;

    const { requirePermission } = require('../src/middleware/permissions');
    const middleware = requirePermission(PERMISSIONS.USER_READ);

    await middleware(unauthenticatedRequest, mockResponse, mockNext);

    expect(mockNext).not.toHaveBeenCalled();
    expect(mockResponse.status).toHaveBeenCalledWith(401);
    expect(mockResponse.json).toHaveBeenCalledWith({
      error: 'Authentication required'
    });
  });
});
