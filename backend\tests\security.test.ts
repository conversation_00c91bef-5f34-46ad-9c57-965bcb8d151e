// Security service tests
import { SecurityService } from '../src/services/securityService';
import { AuditService } from '../src/services/auditService';
import { query } from '../src/config/database';

// Mock the database query function
jest.mock('../src/config/database', () => ({
  query: jest.fn(),
}));

const mockQuery = query as jest.MockedFunction<typeof query>;

describe('SecurityService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validatePassword', () => {
    it('should validate a strong password', () => {
      const password = 'StrongP@ssw0rd123';
      const result = SecurityService.validatePassword(password);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject a weak password', () => {
      const password = '123';
      const result = SecurityService.validatePassword(password);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors).toContain('Password must be at least 8 characters long');
    });

    it('should reject password containing user info', () => {
      const password = 'john123456';
      const userInfo = { firstName: 'John', email: '<EMAIL>' };
      const result = SecurityService.validatePassword(password, userInfo);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password should not contain your personal information');
    });

    it('should reject common passwords', () => {
      const password = 'password123';
      const result = SecurityService.validatePassword(password);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password is too common, please choose a more secure password');
    });
  });

  describe('checkPasswordHistory', () => {
    it('should return true for new password', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [] });
      
      const result = await SecurityService.checkPasswordHistory('user-id', 'newpassword');
      
      expect(result).toBe(true);
      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('SELECT password_hash FROM password_history'),
        ['user-id', 5]
      );
    });

    it('should return false for previously used password', async () => {
      const bcrypt = require('bcryptjs');
      jest.spyOn(bcrypt, 'compare').mockResolvedValueOnce(true);
      
      mockQuery.mockResolvedValueOnce({ 
        rows: [{ password_hash: 'hashed_password' }] 
      });
      
      const result = await SecurityService.checkPasswordHistory('user-id', 'oldpassword');
      
      expect(result).toBe(false);
    });
  });

  describe('checkAccountLockout', () => {
    it('should return not locked for user with no failed attempts', async () => {
      mockQuery.mockResolvedValueOnce({ 
        rows: [{ failed_login_attempts: 0, locked_until: null, last_failed_login: null }] 
      });
      
      const result = await SecurityService.checkAccountLockout('user-id');
      
      expect(result.isLocked).toBe(false);
      expect(result.failedAttempts).toBe(0);
    });

    it('should return locked for user with active lockout', async () => {
      const futureDate = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes from now
      mockQuery.mockResolvedValueOnce({ 
        rows: [{ 
          failed_login_attempts: 5, 
          locked_until: futureDate.toISOString(), 
          last_failed_login: new Date().toISOString() 
        }] 
      });
      
      const result = await SecurityService.checkAccountLockout('user-id');
      
      expect(result.isLocked).toBe(true);
      expect(result.failedAttempts).toBe(5);
      expect(result.lockedUntil).toEqual(futureDate);
    });

    it('should reset failed attempts after timeout', async () => {
      const pastDate = new Date(Date.now() - 2 * 60 * 60 * 1000); // 2 hours ago
      mockQuery
        .mockResolvedValueOnce({ 
          rows: [{ 
            failed_login_attempts: 3, 
            locked_until: null, 
            last_failed_login: pastDate.toISOString() 
          }] 
        })
        .mockResolvedValueOnce({ rows: [] }); // For the reset query
      
      const result = await SecurityService.checkAccountLockout('user-id');
      
      expect(result.isLocked).toBe(false);
      expect(result.failedAttempts).toBe(0);
      expect(mockQuery).toHaveBeenCalledWith(
        'UPDATE users SET failed_login_attempts = 0, last_failed_login = NULL WHERE id = $1',
        ['user-id']
      );
    });
  });

  describe('recordFailedLogin', () => {
    it('should increment failed attempts without locking', async () => {
      mockQuery
        .mockResolvedValueOnce({ rows: [{ failed_login_attempts: 2 }] })
        .mockResolvedValueOnce({ rows: [] });
      
      const result = await SecurityService.recordFailedLogin('user-id');
      
      expect(result.shouldLock).toBe(false);
      expect(result.failedAttempts).toBe(3);
      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE users SET failed_login_attempts = $1'),
        [3, 'user-id']
      );
    });

    it('should lock account after max failed attempts', async () => {
      mockQuery
        .mockResolvedValueOnce({ rows: [{ failed_login_attempts: 4 }] })
        .mockResolvedValueOnce({ rows: [] });
      
      const result = await SecurityService.recordFailedLogin('user-id');
      
      expect(result.shouldLock).toBe(true);
      expect(result.failedAttempts).toBe(5);
      expect(result.lockedUntil).toBeInstanceOf(Date);
    });
  });

  describe('createSession', () => {
    it('should create new session', async () => {
      mockQuery
        .mockResolvedValueOnce({ rows: [{ count: '2' }] }) // Current sessions count
        .mockResolvedValueOnce({ rows: [] }); // Insert session
      
      const sessionData = {
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
        deviceInfo: 'Desktop'
      };
      
      const sessionId = await SecurityService.createSession('user-id', sessionData);
      
      expect(sessionId).toBeDefined();
      expect(sessionId).toHaveLength(64); // 32 bytes = 64 hex chars
      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO user_sessions'),
        expect.arrayContaining(['user-id', '***********', 'Mozilla/5.0', 'Desktop'])
      );
    });

    it('should remove oldest session when limit exceeded', async () => {
      mockQuery
        .mockResolvedValueOnce({ rows: [{ count: '3' }] }) // At limit
        .mockResolvedValueOnce({ rows: [] }) // Delete oldest
        .mockResolvedValueOnce({ rows: [] }); // Insert new
      
      await SecurityService.createSession('user-id', {});
      
      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('DELETE FROM user_sessions'),
        ['user-id']
      );
    });
  });
});

describe('AuditService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('logSecurityEvent', () => {
    it('should log security event successfully', async () => {
      const eventId = 'event-id-123';
      mockQuery.mockResolvedValueOnce({ rows: [{ id: eventId }] });
      
      const event = {
        userId: 'user-id',
        eventType: 'login_success',
        eventDescription: 'User logged in successfully',
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
        success: true,
        metadata: { sessionId: 'session-123' }
      };
      
      const result = await AuditService.logSecurityEvent(event);
      
      expect(result).toBe(eventId);
      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO security_events'),
        [
          'user-id',
          'login_success',
          'User logged in successfully',
          '***********',
          'Mozilla/5.0',
          true,
          JSON.stringify({ sessionId: 'session-123' })
        ]
      );
    });
  });

  describe('logLoginAttempt', () => {
    it('should log login attempt', async () => {
      mockQuery.mockResolvedValueOnce({ rows: [] });
      
      const attempt = {
        email: '<EMAIL>',
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
        success: true
      };
      
      await AuditService.logLoginAttempt(attempt);
      
      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO login_attempts'),
        ['<EMAIL>', '***********', 'Mozilla/5.0', true, null]
      );
    });
  });

  describe('getSecurityEvents', () => {
    it('should fetch security events with filters', async () => {
      const mockEvents = [
        { id: '1', event_type: 'login_success', user_email: '<EMAIL>' }
      ];
      
      mockQuery
        .mockResolvedValueOnce({ rows: [{ total: '1' }] })
        .mockResolvedValueOnce({ rows: mockEvents });
      
      const filters = {
        userId: 'user-id',
        eventType: 'login_success',
        limit: 10,
        offset: 0
      };
      
      const result = await AuditService.getSecurityEvents(filters);
      
      expect(result.total).toBe(1);
      expect(result.events).toEqual(mockEvents);
      expect(mockQuery).toHaveBeenCalledWith(
        expect.stringContaining('WHERE user_id = $1 AND event_type = $2'),
        ['user-id', 'login_success']
      );
    });
  });

  describe('getSecurityStats', () => {
    it('should fetch security statistics', async () => {
      const mockStats = [
        { rows: [{ event_type: 'login_success', count: '10', failures: '0' }] },
        { rows: [{ total_attempts: '15', successful_logins: '12', failed_logins: '3' }] },
        { rows: [{ ip_address: '***********', failed_attempts: '3' }] },
        { rows: [{ locked_accounts: '1' }] },
        { rows: [{ active_sessions: '5' }] }
      ];
      
      mockQuery
        .mockResolvedValueOnce(mockStats[0])
        .mockResolvedValueOnce(mockStats[1])
        .mockResolvedValueOnce(mockStats[2])
        .mockResolvedValueOnce(mockStats[3])
        .mockResolvedValueOnce(mockStats[4]);
      
      const result = await AuditService.getSecurityStats('week');
      
      expect(result.timeframe).toBe('week');
      expect(result.eventsSummary).toEqual(mockStats[0].rows);
      expect(result.loginSummary).toEqual(mockStats[1].rows[0]);
      expect(result.lockedAccounts).toBe(1);
      expect(result.activeSessions).toBe(5);
    });
  });
});
