# Hospital Management System API Documentation

## Overview

This document provides comprehensive documentation for the Hospital Management System API, including authentication, user management, permissions, security features, and organizational management.

## Base URL

```
http://localhost:3001/api/v1
```

## Authentication

All API endpoints (except registration and login) require authentication using JWT tokens.

### Headers

```
Authorization: Bearer <access_token>
Content-Type: application/json
```

## API Endpoints

### Authentication

#### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "role": "nurse"
}
```

**Response:**
```json
{
  "message": "Registration successful. Please check your email for verification.",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "firstName": "<PERSON>",
    "lastName": "Do<PERSON>",
    "role": "nurse"
  }
}
```

#### POST /auth/login
Authenticate user and receive access tokens.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response:**
```json
{
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "nurse"
  },
  "access_token": "jwt_token",
  "refresh_token": "refresh_token",
  "expires_in": 86400,
  "requiresPasswordChange": false
}
```

#### POST /auth/refresh
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refresh_token": "refresh_token"
}
```

#### POST /auth/logout
Logout user and invalidate tokens.

#### POST /auth/change-password
Change user password.

**Request Body:**
```json
{
  "currentPassword": "OldPassword123!",
  "newPassword": "NewPassword123!"
}
```

### User Management

#### GET /management/users
Get list of users with filtering and pagination.

**Query Parameters:**
- `search`: Search by name or email
- `role`: Filter by role
- `status`: Filter by status (active, suspended, pending)
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)
- `sortBy`: Sort field (default: created_at)
- `sortOrder`: Sort order (ASC, DESC)

**Response:**
```json
{
  "users": [
    {
      "id": "uuid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "nurse",
      "emailConfirmed": true,
      "suspended": false,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5,
    "hasNext": true,
    "hasPrev": false
  }
}
```

#### POST /management/users
Create a new user.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "firstName": "Jane",
  "lastName": "Smith",
  "role": "doctor",
  "emailConfirmed": false
}
```

#### PUT /management/users/:id
Update user information.

#### DELETE /management/users/:id
Delete a user (soft delete).

#### POST /management/users/bulk-create
Create multiple users at once.

**Request Body:**
```json
{
  "users": [
    {
      "email": "<EMAIL>",
      "password": "Password123!",
      "firstName": "User",
      "lastName": "One",
      "role": "nurse"
    }
  ]
}
```

### Permissions

#### GET /permissions/available
Get all available permissions and permission groups.

**Response:**
```json
{
  "permissions": [
    "user:read",
    "user:create",
    "user:update",
    "user:delete"
  ],
  "permissionGroups": {
    "user": {
      "name": "User Management",
      "description": "Permissions for managing users",
      "permissions": ["user:read", "user:create", "user:update", "user:delete"]
    }
  }
}
```

#### GET /permissions/user/:userId
Get permissions for a specific user.

#### GET /permissions/me
Get current user's permissions.

#### POST /permissions/check
Check if user has a specific permission.

**Request Body:**
```json
{
  "permission": "user:create",
  "userId": "optional-user-id"
}
```

#### PUT /permissions/role/:roleId
Update permissions for a role.

**Request Body:**
```json
{
  "permissions": ["user:read", "user:create", "employee:read"]
}
```

### Security

#### GET /security/stats
Get security statistics and metrics.

**Query Parameters:**
- `timeframe`: day, week, month (default: week)

**Response:**
```json
{
  "stats": {
    "timeframe": "week",
    "eventsSummary": [
      {
        "event_type": "login_success",
        "count": "150",
        "failures": "0"
      }
    ],
    "loginSummary": {
      "total_attempts": "200",
      "successful_logins": "180",
      "failed_logins": "20",
      "unique_users": "45",
      "unique_ips": "38"
    },
    "lockedAccounts": 2,
    "activeSessions": 25
  }
}
```

#### GET /security/events
Get security events with filtering.

**Query Parameters:**
- `userId`: Filter by user ID
- `eventType`: Filter by event type
- `startDate`: Start date filter
- `endDate`: End date filter
- `success`: Filter by success status
- `limit`: Items per page
- `offset`: Pagination offset

#### GET /security/sessions
Get current user's active sessions.

#### DELETE /security/sessions/:sessionId
Revoke a specific session.

#### POST /security/sessions/revoke-all
Revoke all other sessions except current.

#### POST /security/validate-password
Validate password strength.

**Request Body:**
```json
{
  "password": "TestPassword123!",
  "userInfo": {
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe"
  }
}
```

**Response:**
```json
{
  "isValid": true,
  "errors": [],
  "strength": {
    "score": 8,
    "level": "strong",
    "feedback": []
  }
}
```

### Management

#### GET /management/departments
Get all departments.

#### POST /management/departments
Create a new department.

**Request Body:**
```json
{
  "name": "Cardiology",
  "description": "Heart and cardiovascular care",
  "managerId": "optional-manager-uuid",
  "isActive": true
}
```

#### GET /management/departments/hierarchy
Get department hierarchy tree.

#### GET /management/departments/:id/employees
Get employees in a specific department.

#### GET /management/positions
Get all positions.

#### GET /management/positions/hierarchy
Get position hierarchy.

#### GET /management/roles
Get all roles.

#### GET /management/organization/chart
Get organizational chart data.

#### GET /management/organization/stats
Get organizational statistics.

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": "Additional error details"
}
```

### Common HTTP Status Codes

- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `409`: Conflict
- `422`: Validation Error
- `500`: Internal Server Error

## Rate Limiting

API requests are rate limited to prevent abuse:
- 100 requests per minute for authenticated users
- 20 requests per minute for unauthenticated users

## Pagination

List endpoints support pagination with the following parameters:
- `page`: Page number (1-based)
- `limit`: Items per page (max 100)

Pagination response includes:
```json
{
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## Security Features

### Password Policy
- Minimum 8 characters
- Must contain uppercase, lowercase, numbers, and special characters
- Cannot contain user information
- Cannot reuse last 5 passwords
- Expires after 90 days

### Account Lockout
- Account locked after 5 failed login attempts
- Lockout duration: 30 minutes
- Failed attempts reset after 60 minutes of inactivity

### Session Management
- Maximum 3 concurrent sessions per user
- Session timeout: 24 hours
- Session timeout warning: 5 minutes before expiry

### Audit Logging
All security-related events are logged including:
- Login attempts (successful and failed)
- Password changes
- Permission changes
- Account lockouts
- Session management

## Permission System

The system uses a role-based permission system with the following structure:

### Permission Format
Permissions follow the format: `resource:action`
- `user:read` - Read user information
- `user:create` - Create new users
- `employee:update` - Update employee information

### Default Role Permissions

#### Admin
- Full system access (`system:admin`)
- All user management permissions
- All organizational management permissions

#### Manager
- Employee management
- Schedule management
- Leave request approval
- Department oversight

#### Doctor/Nurse/Other Roles
- Limited permissions based on job function
- Own profile management
- Schedule viewing
- Leave request creation

## WebSocket Events

Real-time updates are provided via WebSocket connections:

### Connection
```javascript
const socket = io('ws://localhost:3001', {
  auth: {
    token: 'jwt_token'
  }
});
```

### Events
- `user:updated` - User information changed
- `schedule:updated` - Schedule changes
- `security:alert` - Security events
- `system:notification` - System notifications

## Testing

### Running Tests

```bash
# Backend tests
cd backend
npm test

# Frontend tests
cd frontend
npm test

# Integration tests
npm run test:integration
```

### Test Coverage

The system includes comprehensive test coverage for:
- Authentication and authorization
- Permission system
- Security features
- User management
- API endpoints
- Database operations

## Development Setup

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- Redis (optional, for caching)

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   cd backend && npm install
   cd ../frontend && npm install
   ```
3. Set up environment variables
4. Run database migrations
5. Start development servers

### Environment Variables

See `.env.example` files in both backend and frontend directories for required environment variables.
