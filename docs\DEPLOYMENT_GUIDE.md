# Hospital Management System - Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the Hospital Management System in production environments.

## Prerequisites

### System Requirements

- **Operating System**: Ubuntu 20.04+ or CentOS 8+
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: Minimum 50GB SSD
- **CPU**: 2+ cores

### Software Dependencies

- Node.js 18.x or higher
- PostgreSQL 14.x or higher
- Nginx (for reverse proxy)
- PM2 (for process management)
- SSL certificate (Let's Encrypt recommended)

## Database Setup

### 1. Install PostgreSQL

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# CentOS/RHEL
sudo dnf install postgresql postgresql-server postgresql-contrib
sudo postgresql-setup --initdb
sudo systemctl enable postgresql
sudo systemctl start postgresql
```

### 2. Create Database and User

```bash
sudo -u postgres psql

-- Create database
CREATE DATABASE hospital_management;

-- Create user
CREATE USER hospital_user WITH ENCRYPTED PASSWORD 'secure_password_here';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE hospital_management TO hospital_user;

-- Exit psql
\q
```

### 3. Configure PostgreSQL

Edit `/etc/postgresql/14/main/postgresql.conf`:

```conf
# Connection settings
listen_addresses = 'localhost'
port = 5432

# Memory settings
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB

# Logging
log_statement = 'all'
log_duration = on
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
```

Edit `/etc/postgresql/14/main/pg_hba.conf`:

```conf
# Local connections
local   all             all                                     peer
host    all             all             127.0.0.1/32            md5
host    all             all             ::1/128                 md5
```

Restart PostgreSQL:

```bash
sudo systemctl restart postgresql
```

## Application Deployment

### 1. Create Application User

```bash
sudo useradd -m -s /bin/bash hospital
sudo usermod -aG sudo hospital
```

### 2. Clone and Setup Application

```bash
# Switch to application user
sudo su - hospital

# Clone repository
git clone https://github.com/your-org/hospital-management.git
cd hospital-management

# Install Node.js (using NodeSource)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 globally
sudo npm install -g pm2
```

### 3. Backend Setup

```bash
cd backend

# Install dependencies
npm ci --production

# Create environment file
cp .env.example .env

# Edit environment variables
nano .env
```

#### Environment Variables (.env)

```env
# Database
DATABASE_URL=postgresql://hospital_user:secure_password_here@localhost:5432/hospital_management

# JWT
JWT_SECRET=your_super_secure_jwt_secret_here
JWT_REFRESH_SECRET=your_super_secure_refresh_secret_here
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Server
PORT=3001
NODE_ENV=production
API_PREFIX=/api/v1

# Email (configure based on your provider)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS
CORS_ORIGIN=https://yourdomain.com

# File uploads
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,application/pdf

# Cache (if using Redis)
REDIS_URL=redis://localhost:6379
```

### 4. Run Database Migrations

```bash
# Run migrations
npm run migrate

# Seed initial data (optional)
npm run seed
```

### 5. Frontend Setup

```bash
cd ../frontend

# Install dependencies
npm ci

# Create environment file
cp .env.example .env.production

# Edit environment variables
nano .env.production
```

#### Frontend Environment Variables

```env
VITE_API_URL=https://api.yourdomain.com/api/v1
VITE_APP_NAME=Hospital Management System
VITE_APP_VERSION=1.0.0
```

### 6. Build Frontend

```bash
# Build for production
npm run build

# The build files will be in the 'dist' directory
```

## Process Management with PM2

### 1. Create PM2 Configuration

Create `ecosystem.config.js` in the project root:

```javascript
module.exports = {
  apps: [
    {
      name: 'hospital-backend',
      script: './backend/dist/server.js',
      cwd: './backend',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      error_file: './logs/err.log',
      out_file: './logs/out.log',
      log_file: './logs/combined.log',
      time: true,
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024'
    }
  ]
};
```

### 2. Start Application with PM2

```bash
# Create logs directory
mkdir -p logs

# Start application
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
pm2 startup
# Follow the instructions provided by the command above
```

## Nginx Configuration

### 1. Install Nginx

```bash
sudo apt update
sudo apt install nginx
```

### 2. Create Nginx Configuration

Create `/etc/nginx/sites-available/hospital-management`:

```nginx
# Rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;

server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin";

    # Frontend (React app)
    location / {
        root /home/<USER>/hospital-management/frontend/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API Backend
    location /api/ {
        # Rate limiting
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Stricter rate limiting for login endpoint
    location /api/v1/auth/login {
        limit_req zone=login burst=5 nodelay;
        
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # File upload size limit
    client_max_body_size 10M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
```

### 3. Enable Site and Test Configuration

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/hospital-management /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

## SSL Certificate Setup

### 1. Install Certbot

```bash
sudo apt install certbot python3-certbot-nginx
```

### 2. Obtain SSL Certificate

```bash
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
```

### 3. Setup Auto-renewal

```bash
# Test renewal
sudo certbot renew --dry-run

# Add to crontab
sudo crontab -e

# Add this line:
0 12 * * * /usr/bin/certbot renew --quiet
```

## Monitoring and Logging

### 1. Setup Log Rotation

Create `/etc/logrotate.d/hospital-management`:

```
/home/<USER>/hospital-management/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 hospital hospital
    postrotate
        pm2 reloadLogs
    endscript
}
```

### 2. Setup Monitoring

```bash
# Install monitoring tools
sudo apt install htop iotop

# Setup PM2 monitoring
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
```

## Backup Strategy

### 1. Database Backup Script

Create `/home/<USER>/scripts/backup-db.sh`:

```bash
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="hospital_management"
DB_USER="hospital_user"

mkdir -p $BACKUP_DIR

# Create backup
pg_dump -U $DB_USER -h localhost $DB_NAME | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# Keep only last 30 days of backups
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +30 -delete

echo "Database backup completed: db_backup_$DATE.sql.gz"
```

### 2. Setup Automated Backups

```bash
# Make script executable
chmod +x /home/<USER>/scripts/backup-db.sh

# Add to crontab
crontab -e

# Add daily backup at 2 AM
0 2 * * * /home/<USER>/scripts/backup-db.sh
```

## Security Checklist

- [ ] Database user has minimal required permissions
- [ ] Strong passwords for all accounts
- [ ] SSL/TLS certificates properly configured
- [ ] Security headers configured in Nginx
- [ ] Rate limiting enabled
- [ ] Firewall configured (UFW recommended)
- [ ] Regular security updates scheduled
- [ ] Log monitoring setup
- [ ] Backup strategy implemented
- [ ] Environment variables secured

## Maintenance

### Regular Tasks

1. **Weekly**: Review logs for errors and security issues
2. **Monthly**: Update system packages and dependencies
3. **Quarterly**: Review and rotate secrets/passwords
4. **Annually**: Review and update SSL certificates

### Commands for Maintenance

```bash
# Update system packages
sudo apt update && sudo apt upgrade

# Update Node.js dependencies
cd /home/<USER>/hospital-management/backend
npm audit fix

# Restart application
pm2 restart all

# View logs
pm2 logs

# Monitor system resources
htop
```

## Troubleshooting

### Common Issues

1. **Application won't start**: Check logs with `pm2 logs`
2. **Database connection issues**: Verify credentials and PostgreSQL status
3. **High memory usage**: Check for memory leaks, restart application
4. **SSL certificate issues**: Verify certificate validity and renewal

### Useful Commands

```bash
# Check application status
pm2 status

# View real-time logs
pm2 logs --lines 100

# Restart application
pm2 restart hospital-backend

# Check database connections
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"

# Check Nginx status
sudo systemctl status nginx

# Test Nginx configuration
sudo nginx -t
```
