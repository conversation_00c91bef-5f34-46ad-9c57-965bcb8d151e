import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useQuery } from '@tanstack/react-query';
import { securityApi } from '@/lib/api';
import { 
  Shield, 
  AlertTriangle, 
  Activity, 
  Users, 
  Lock, 
  Unlock,
  Eye,
  Calendar,
  TrendingUp,
  TrendingDown,
  BarChart3
} from 'lucide-react';

export function SecurityDashboard() {
  const [timeframe, setTimeframe] = useState<'day' | 'week' | 'month'>('week');
  const [activeTab, setActiveTab] = useState('overview');

  const { data: securityStats, isLoading: isLoadingStats } = useQuery({
    queryKey: ['security-stats', timeframe],
    queryFn: async () => {
      const response = await securityApi.getSecurityStats(timeframe);
      return response.stats;
    },
  });

  const { data: securityEvents, isLoading: isLoadingEvents } = useQuery({
    queryKey: ['security-events'],
    queryFn: async () => {
      const response = await securityApi.getSecurityEvents({ limit: 20 });
      return response;
    },
  });

  const { data: loginAttempts, isLoading: isLoadingAttempts } = useQuery({
    queryKey: ['login-attempts'],
    queryFn: async () => {
      const response = await securityApi.getLoginAttempts({ limit: 20 });
      return response;
    },
  });

  if (isLoadingStats) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Security Dashboard</h2>
          <p className="text-gray-600">Monitor security events and system health</p>
        </div>
        
        <Select value={timeframe} onValueChange={(value: any) => setTimeframe(value)}>
          <SelectTrigger className="w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="day">Last 24 Hours</SelectItem>
            <SelectItem value="week">Last 7 Days</SelectItem>
            <SelectItem value="month">Last 30 Days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="events">Security Events</TabsTrigger>
          <TabsTrigger value="logins">Login Attempts</TabsTrigger>
          <TabsTrigger value="sessions">Active Sessions</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Security Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Login Attempts</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {securityStats?.loginSummary?.total_attempts || 0}
                </div>
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <TrendingUp className="h-3 w-3 text-green-500" />
                  Success rate: {
                    securityStats?.loginSummary?.total_attempts > 0
                      ? Math.round((securityStats.loginSummary.successful_logins / securityStats.loginSummary.total_attempts) * 100)
                      : 0
                  }%
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Failed Logins</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">
                  {securityStats?.loginSummary?.failed_logins || 0}
                </div>
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <TrendingDown className="h-3 w-3 text-red-500" />
                  From {securityStats?.loginSummary?.unique_ips || 0} unique IPs
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Locked Accounts</CardTitle>
                <Lock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  {securityStats?.lockedAccounts || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Accounts currently locked
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Sessions</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {securityStats?.activeSessions || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Currently active user sessions
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Security Events Summary */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Security Events Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {securityStats?.eventsSummary?.map((event: any, index: number) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${
                          event.failures > 0 ? 'bg-red-500' : 'bg-green-500'
                        }`}></div>
                        <span className="text-sm capitalize">
                          {event.event_type.replace(/_/g, ' ')}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{event.count}</Badge>
                        {event.failures > 0 && (
                          <Badge variant="destructive">{event.failures} failed</Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Top Failed Login IPs
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {securityStats?.topFailedIPs?.slice(0, 5).map((ip: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-red-50 rounded">
                      <span className="text-sm font-mono">{ip.ip_address}</span>
                      <Badge variant="destructive">{ip.failed_attempts} attempts</Badge>
                    </div>
                  ))}
                  {(!securityStats?.topFailedIPs || securityStats.topFailedIPs.length === 0) && (
                    <p className="text-sm text-gray-500 text-center py-4">
                      No failed login attempts detected
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="events" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Recent Security Events
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingEvents ? (
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Time</TableHead>
                      <TableHead>User</TableHead>
                      <TableHead>Event</TableHead>
                      <TableHead>IP Address</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {securityEvents?.events?.map((event: any) => (
                      <TableRow key={event.id}>
                        <TableCell className="text-sm">
                          {new Date(event.created_at).toLocaleString()}
                        </TableCell>
                        <TableCell>
                          {event.user_email || 'System'}
                        </TableCell>
                        <TableCell>
                          <span className="capitalize">
                            {event.event_type.replace(/_/g, ' ')}
                          </span>
                        </TableCell>
                        <TableCell className="font-mono text-sm">
                          {event.ip_address || '-'}
                        </TableCell>
                        <TableCell>
                          <Badge variant={event.success ? 'default' : 'destructive'}>
                            {event.success ? 'Success' : 'Failed'}
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logins" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Recent Login Attempts
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingAttempts ? (
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Time</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>IP Address</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Failure Reason</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loginAttempts?.attempts?.map((attempt: any) => (
                      <TableRow key={attempt.id}>
                        <TableCell className="text-sm">
                          {new Date(attempt.created_at).toLocaleString()}
                        </TableCell>
                        <TableCell>{attempt.email}</TableCell>
                        <TableCell className="font-mono text-sm">
                          {attempt.ip_address || '-'}
                        </TableCell>
                        <TableCell>
                          <Badge variant={attempt.success ? 'default' : 'destructive'}>
                            {attempt.success ? 'Success' : 'Failed'}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm">
                          {attempt.failure_reason || '-'}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sessions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Active User Sessions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-500 text-center py-8">
                Session management will be implemented in the next phase
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
